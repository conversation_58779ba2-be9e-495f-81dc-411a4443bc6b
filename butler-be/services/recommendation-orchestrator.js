import {
  smartPreFilter,
  enhancedSemanticSearch,
  optimizedAIProcessing,
  getCacheStats,
  clearRecommendationCache,
} from "./optimized-recommendation-service.js";
import { getRecommendations as getFallbackRecommendations } from "./menu-search-service.js";
import { getPersonalizedRecommendations } from "./personalization-service.js";
import {
  shouldUseOptimizedSystem,
  recordABTestMetrics,
} from "./ab-testing-service.js";

/**
 * Recommendation Orchestrator
 * Main service that coordinates the 3-stage optimization process
 * Provides fallback mechanisms and performance monitoring
 */

// Performance metrics tracking
let performanceMetrics = {
  totalRequests: 0,
  tokensSaved: 0,
  averageResponseTime: 0,
  cacheHitRate: 0,
  fallbackUsage: 0,
  lastReset: new Date(),
};

/**
 * Main recommendation function - replaces the current system
 * @param {string} userMessage - User's query
 * @param {Array} availableDishes - All available dishes
 * @param {string} userId - User ID
 * @param {Object} context - Additional context
 * @returns {Promise<Object>} - Optimized recommendations
 */
export const getOptimizedRecommendations = async (
  userMessage,
  availableDishes,
  userId,
  context = {}
) => {
  const startTime = Date.now();
  performanceMetrics.totalRequests++;

  // A/B Testing: Determine which system to use
  const useOptimizedSystem = shouldUseOptimizedSystem(userId, context);
  const variant = useOptimizedSystem ? "treatment" : "control";

  console.log(`🧪 A/B Test: User ${userId} assigned to ${variant} group`);

  try {
    if (!useOptimizedSystem) {
      // Use fallback system for control group
      console.log(`🔄 Using control system (fallback) for user ${userId}`);
      const fallbackResult = await getFallbackRecommendations(
        userMessage,
        availableDishes,
        {
          userId,
          ...context,
          fallbackReason: "A/B test control group",
        }
      );

      // Record A/B test metrics for control group
      recordABTestMetrics("control", {
        responseTime: Date.now() - startTime,
        tokenUsage: 0, // Fallback doesn't use tokens
        successful: fallbackResult.recommendations?.length > 0,
        cacheHits: 0,
        cacheMisses: 0,
        fallbackUsed: true,
      });

      return {
        ...fallbackResult,
        abTestVariant: "control",
        abTestUsed: true,
      };
    }

    console.log(`🚀 Starting optimized recommendation for user ${userId}`);
    console.log(`📊 Processing ${availableDishes.length} available dishes`);

    // Stage 1: Smart Pre-filtering (No AI)
    console.log("🔍 Stage 1: Smart Pre-filtering...");
    const preFilterResult = await smartPreFilter(
      userMessage,
      availableDishes,
      userId,
      context
    );

    console.log(
      `✅ Pre-filtering complete: ${preFilterResult.dishes.length} dishes selected`
    );
    console.log(
      `📉 Reduced by ${preFilterResult.filterStats.reductionPercentage}%`
    );

    // Stage 2: Enhanced Semantic Search
    console.log("🔎 Stage 2: Semantic Search...");
    const semanticResult = await enhancedSemanticSearch(
      userMessage,
      preFilterResult.dishes,
      context
    );

    console.log(
      `✅ Semantic search complete: ${semanticResult.dishes.length} relevant dishes found`
    );

    // Stage 3: Optimized AI Processing
    console.log("🤖 Stage 3: AI Processing...");
    const aiResult = await optimizedAIProcessing(
      userMessage,
      semanticResult.dishes,
      {
        ...context,
        lastConversation: context.lastConversation || [],
        cartHistory: context.cartHistory || [],
      }
    );

    console.log(`✅ AI processing complete`);
    console.log(
      `💰 Estimated token usage: ${aiResult.tokenUsage.estimatedTokens}`
    );
    console.log(
      `📊 Menu reduction: ${aiResult.tokenUsage.reductionFromFullMenu}`
    );

    // Map AI recommended dish IDs to actual dishes
    const recommendedDishes = mapRecommendedDishes(
      aiResult.aiResponse.recommendedDishIds,
      semanticResult.dishes,
      preFilterResult.dishes
    );

    // Calculate performance metrics
    const responseTime = Date.now() - startTime;
    updatePerformanceMetrics(
      responseTime,
      aiResult,
      preFilterResult,
      semanticResult
    );

    const result = {
      recommendations: recommendedDishes,
      aiResponse: aiResult.aiResponse,
      performance: {
        responseTime,
        stagesCompleted: 3,
        dishesProcessed: {
          original: availableDishes.length,
          afterPreFilter: preFilterResult.dishes.length,
          afterSemantic: semanticResult.dishes.length,
          sentToAI: semanticResult.dishes.length,
        },
        tokenOptimization: {
          estimatedTokens: aiResult.tokenUsage.estimatedTokens,
          reductionFromFullMenu: aiResult.tokenUsage.reductionFromFullMenu,
          dishesReduction: `${availableDishes.length} → ${semanticResult.dishes.length}`,
        },
        cacheUsage: {
          preFilterCached: preFilterResult.fromCache || false,
          semanticCached: semanticResult.fromCache || false,
          aiCached: aiResult.fromCache || false,
        },
      },
      fallbackUsed: false,
      optimizationVersion: "1.0",
      abTestVariant: "treatment",
      abTestUsed: true,
    };

    // Record A/B test metrics for treatment group
    recordABTestMetrics("treatment", {
      responseTime,
      tokenUsage: aiResult.tokenUsage.estimatedTokens,
      successful: recommendedDishes.length > 0,
      cacheHits: Object.values(result.performance.cacheUsage).filter(Boolean)
        .length,
      cacheMisses: Object.values(result.performance.cacheUsage).filter(
        (hit) => !hit
      ).length,
      fallbackUsed: false,
    });

    console.log(`🎉 Optimization complete in ${responseTime}ms`);
    return result;
  } catch (error) {
    console.error("❌ Error in optimized recommendations:", error);

    // Fallback to existing system
    console.log("🔄 Falling back to existing recommendation system...");
    performanceMetrics.fallbackUsage++;

    return await getFallbackRecommendations(userMessage, availableDishes, {
      userId,
      ...context,
      fallbackReason: error.message,
      responseTime: Date.now() - startTime,
    });
  }
};

/**
 * Fallback recommendation system
 * Uses existing services when optimization fails
 */
const getFallbackRecommendations = async (
  userMessage,
  availableDishes,
  context
) => {
  try {
    // Use existing personalization service as fallback
    const personalizedRecs = await getPersonalizedRecommendations(
      context.userId,
      availableDishes,
      userMessage,
      context.outletId
    );

    if (personalizedRecs && personalizedRecs.length > 0) {
      return {
        recommendations: personalizedRecs.slice(0, 5),
        aiResponse: {
          keywords: [],
          aiMessage: "Here are some personalized recommendations for you.",
          recommendedDishIds: personalizedRecs
            .slice(0, 5)
            .map((dish) => dish._id),
          faqSuggestions: [
            "What's popular today?",
            "Show me vegetarian options",
          ],
          detectedLanguage: context.language || "en",
        },
        performance: {
          responseTime: context.responseTime || 0,
          stagesCompleted: 0,
          fallbackMethod: "personalization",
        },
        fallbackUsed: true,
        fallbackReason: context.fallbackReason,
      };
    }

    // Final fallback - popular dishes
    const popularDishes = availableDishes
      .filter((dish) => dish.isAvailable)
      .sort((a, b) => (b.ratings?.average || 0) - (a.ratings?.average || 0))
      .slice(0, 5);

    return {
      recommendations: popularDishes,
      aiResponse: {
        keywords: [],
        aiMessage: "Here are our popular dishes you might enjoy.",
        recommendedDishIds: popularDishes.map((dish) => dish._id),
        faqSuggestions: ["What's your specialty?", "Show me today's specials"],
        detectedLanguage: context.language || "en",
      },
      performance: {
        responseTime: context.responseTime || 0,
        stagesCompleted: 0,
        fallbackMethod: "popular",
      },
      fallbackUsed: true,
      fallbackReason: context.fallbackReason,
    };
  } catch (fallbackError) {
    console.error("❌ Fallback system also failed:", fallbackError);

    // Ultimate fallback - first 5 available dishes
    const basicDishes = availableDishes
      .filter((dish) => dish.isAvailable)
      .slice(0, 5);

    return {
      recommendations: basicDishes,
      aiResponse: {
        keywords: [],
        aiMessage: "I can help you explore our menu.",
        recommendedDishIds: basicDishes.map((dish) => dish._id),
        faqSuggestions: ["What do you recommend?", "Show me the menu"],
        detectedLanguage: "en",
      },
      performance: {
        responseTime: context.responseTime || 0,
        stagesCompleted: 0,
        fallbackMethod: "basic",
      },
      fallbackUsed: true,
      fallbackReason: `Primary: ${context.fallbackReason}, Fallback: ${fallbackError.message}`,
    };
  }
};

/**
 * Map AI recommended dish IDs to actual dish objects
 */
const mapRecommendedDishes = (
  recommendedIds,
  semanticDishes,
  preFilteredDishes
) => {
  if (!recommendedIds || recommendedIds.length === 0) {
    // If AI didn't recommend specific dishes, return top semantic results
    return semanticDishes.slice(0, 5);
  }

  const recommendedDishes = [];
  const allDishes = [...semanticDishes, ...preFilteredDishes];

  // Find dishes by ID
  recommendedIds.forEach((id) => {
    const dish = allDishes.find((d) => d._id.toString() === id.toString());
    if (
      dish &&
      !recommendedDishes.find((rd) => rd._id.toString() === dish._id.toString())
    ) {
      recommendedDishes.push(dish);
    }
  });

  // Fill remaining slots with top semantic results if needed
  if (recommendedDishes.length < 3) {
    semanticDishes.forEach((dish) => {
      if (
        recommendedDishes.length < 5 &&
        !recommendedDishes.find(
          (rd) => rd._id.toString() === dish._id.toString()
        )
      ) {
        recommendedDishes.push(dish);
      }
    });
  }

  return recommendedDishes.slice(0, 5);
};

/**
 * Update performance metrics
 */
const updatePerformanceMetrics = (
  responseTime,
  aiResult,
  preFilterResult,
  semanticResult
) => {
  // Update average response time
  performanceMetrics.averageResponseTime =
    (performanceMetrics.averageResponseTime *
      (performanceMetrics.totalRequests - 1) +
      responseTime) /
    performanceMetrics.totalRequests;

  // Calculate cache hit rate
  const cacheHits = [
    preFilterResult.fromCache,
    semanticResult.fromCache,
    aiResult.fromCache,
  ].filter(Boolean).length;

  const currentCacheRate = (cacheHits / 3) * 100;
  performanceMetrics.cacheHitRate =
    (performanceMetrics.cacheHitRate * (performanceMetrics.totalRequests - 1) +
      currentCacheRate) /
    performanceMetrics.totalRequests;

  // Estimate tokens saved (rough calculation)
  const estimatedOriginalTokens = 200 * 50; // Assuming 200 dishes * 50 tokens each
  const actualTokens = aiResult.tokenUsage.estimatedTokens || 0;
  performanceMetrics.tokensSaved += Math.max(
    0,
    estimatedOriginalTokens - actualTokens
  );
};

/**
 * Get performance metrics and statistics
 */
export const getPerformanceMetrics = () => {
  return {
    ...performanceMetrics,
    cacheStats: getCacheStats(),
    uptime: Date.now() - performanceMetrics.lastReset.getTime(),
    efficiency: {
      averageTokensSavedPerRequest:
        performanceMetrics.tokensSaved /
        Math.max(1, performanceMetrics.totalRequests),
      fallbackRate:
        (performanceMetrics.fallbackUsage /
          Math.max(1, performanceMetrics.totalRequests)) *
        100,
      successRate:
        ((performanceMetrics.totalRequests - performanceMetrics.fallbackUsage) /
          Math.max(1, performanceMetrics.totalRequests)) *
        100,
    },
  };
};

/**
 * Reset performance metrics
 */
export const resetPerformanceMetrics = () => {
  performanceMetrics = {
    totalRequests: 0,
    tokensSaved: 0,
    averageResponseTime: 0,
    cacheHitRate: 0,
    fallbackUsage: 0,
    lastReset: new Date(),
  };
};

/**
 * Health check for the optimization system
 */
export const healthCheck = async () => {
  try {
    const metrics = getPerformanceMetrics();
    const cacheStats = getCacheStats();

    return {
      status: "healthy",
      version: "1.0",
      metrics: {
        totalRequests: metrics.totalRequests,
        averageResponseTime: `${metrics.averageResponseTime.toFixed(2)}ms`,
        cacheHitRate: `${metrics.cacheHitRate.toFixed(1)}%`,
        successRate: `${metrics.efficiency.successRate.toFixed(1)}%`,
        fallbackRate: `${metrics.efficiency.fallbackRate.toFixed(1)}%`,
      },
      cacheHealth: {
        query: cacheStats.query.keys,
        preference: cacheStats.preference.keys,
        ai: cacheStats.ai.keys,
      },
      lastReset: metrics.lastReset,
    };
  } catch (error) {
    return {
      status: "error",
      error: error.message,
      timestamp: new Date(),
    };
  }
};

// Cache management exports
export { clearRecommendationCache };
